{"fofa": {"fields": {"ip": "ip=", "port": "port=", "body": "body=", "title": "title=", "header": "header=", "server": "server=", "domain": "domain=", "host": "host=", "protocol": "protocol=", "banner": "banner=", "cert": "cert=", "app": "app=", "os": "os="}, "operators": {"equal": "=", "strict_equal": "==", "and": "&&", "or": "||", "not_equal": "!=", "left_paren": "(", "right_paren": ")"}}, "quake": {"fields": {"ip": "ip:", "port": "port:", "body": "response.body:", "title": "response.title:", "header": "response.headers:", "server": "response.headers.server:", "domain": "domain:", "host": "hostname:", "protocol": "transport:", "banner": "response.raw:", "cert": "ssl.cert:", "app": "service.name:", "os": "os:"}, "operators": {"equal": ":", "strict_equal": ":", "and": "AND", "or": "OR ", "not_equal": "NOT", "left_paren": "(", "right_paren": ")"}}, "zoomeye": {"fields": {"ip": "ip:", "port": "port:", "body": "body:", "title": "title:", "header": "headers:", "server": "server:", "domain": "site:", "host": "hostname:", "protocol": "service:", "banner": "banner:", "cert": "ssl:", "app": "app:", "os": "os:"}, "operators": {"equal": "=", "strict_equal": "==", "and": "&&", "or": "||", "not_equal": "!=", "left_paren": "(", "right_paren": ")"}}, "hunter": {"fields": {"ip": "ip=", "port": "port=", "body": "web.body=", "title": "web.title=", "header": "header=", "server": "web.server=", "domain": "domain=", "host": "domain=", "protocol": "protocol=", "banner": "banner=", "cert": "cert=", "app": "app=", "os": "os="}, "operators": {"equal": "=", "strict_equal": "==", "and": "&&", "or": "||", "not_equal": "!=", "left_paren": "(", "right_paren": ")"}}, "threatbook": {"fields": {"ip": "ip:", "port": "port:", "body": "body:", "title": "title:", "header": "header:", "server": "server:", "domain": "domain:", "host": "host:", "protocol": "protocol:", "banner": "banner:", "cert": "cert:", "app": "app:", "os": "os:"}, "operators": {"equal": "=", "strict_equal": "==", "and": "&&", "or": "||", "not_equal": "!=", "left_paren": "(", "right_paren": ")"}}}