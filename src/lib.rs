//! # ConvertiX - 网络空间资产测绘查询语句转换工具
//! 
//! 一个用于在不同网络空间资产测绘平台间转换查询语句的Rust库。
//! 
//! ## 支持的平台
//! 
//! - FOFA
//! - Quake  
//! - Zoomeye
//! - Hunter
//! - Threatbook
//! 
//! ## 基本用法
//!
//! ```rust
//! use ConvertiX::QueryConverter;
//!
//! let converter = QueryConverter::new();
//! let result = converter.convert(
//!     r#"ip="*******" && port="80""#,
//!     "fofa",
//!     "quake"
//! ).unwrap();
//!
//! println!("转换结果: {}", result);
//! ```

pub mod converter;
pub mod config;

pub use converter::QueryConverter;
pub use config::{PlatformConfig, OperatorConfig};

/// 便捷函数：从配置文件快速转换查询语句
///
/// # 参数
///
/// * `config_path` - 配置文件路径
/// * `query` - 要转换的查询语句
/// * `from` - 源平台名称
/// * `to` - 目标平台名称
///
/// # 返回值
///
/// 返回转换后的查询语句，如果转换失败则返回错误信息
///
/// # 示例
///
/// ```rust,no_run
/// use ConvertiX::convert_query_with_config;
///
/// let result = convert_query_with_config(
///     "config.json",
///     r#"ip="*******" && port="80""#,
///     "fofa",
///     "quake"
/// ).unwrap();
/// ```
pub fn convert_query_with_config(config_path: &str, query: &str, from: &str, to: &str) -> Result<String, Box<dyn std::error::Error>> {
    let converter = QueryConverter::from_config_file(config_path)?;
    Ok(converter.convert(query, from, to)?)
}

/// 从配置文件获取所有支持的平台列表
///
/// # 参数
///
/// * `config_path` - 配置文件路径
///
/// # 返回值
///
/// 返回包含所有支持平台名称的向量
///
/// # 示例
///
/// ```rust,no_run
/// use ConvertiX::get_supported_platforms_from_config;
///
/// let platforms = get_supported_platforms_from_config("config.json").unwrap();
/// println!("支持的平台: {:?}", platforms);
/// ```
pub fn get_supported_platforms_from_config(config_path: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let converter = QueryConverter::from_config_file(config_path)?;
    Ok(converter.get_supported_platforms())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;

    fn create_test_config() -> &'static str {
        r#"{
  "fofa": {
    "fields": {
      "ip": "ip=",
      "port": "port="
    },
    "operators": {
      "equal": "=",
      "strict_equal": "==",
      "and": " && ",
      "or": " || ",
      "not_equal": "!=",
      "left_paren": "(",
      "right_paren": ")"
    }
  },
  "quake": {
    "fields": {
      "ip": "ip:",
      "port": "port:"
    },
    "operators": {
      "equal": ":",
      "strict_equal": ":",
      "and": " AND ",
      "or": " OR ",
      "not_equal": " NOT ",
      "left_paren": "(",
      "right_paren": ")"
    }
  }
}"#
    }

    #[test]
    fn test_fofa_to_quake_conversion() {
        let config_content = create_test_config();
        let test_file = "test_config_fofa_quake.json";
        fs::write(test_file, config_content).unwrap();

        let result = convert_query_with_config(
            test_file,
            r#"ip="*******" && port="80""#,
            "fofa",
            "quake"
        ).unwrap();

        assert!(result.contains("ip:"));
        assert!(result.contains("AND"));

        let _ = fs::remove_file(test_file);
    }

    #[test]
    fn test_quake_to_fofa_conversion() {
        let config_content = create_test_config();
        let test_file = "test_config_quake_fofa.json";
        fs::write(test_file, config_content).unwrap();

        let result = convert_query_with_config(
            test_file,
            r#"ip:"*******" AND port:"80""#,
            "quake",
            "fofa"
        ).unwrap();

        assert!(result.contains("ip="));
        assert!(result.contains("&&"));

        let _ = fs::remove_file(test_file);
    }

    #[test]
    fn test_supported_platforms_from_config() {
        let config_content = create_test_config();
        fs::write("test_config.json", config_content).unwrap();

        let platforms = get_supported_platforms_from_config("test_config.json").unwrap();

        assert!(platforms.contains(&"fofa".to_string()));
        assert!(platforms.contains(&"quake".to_string()));

        fs::remove_file("test_config.json").unwrap();
    }

    #[test]
    fn test_invalid_config_file() {
        let result = convert_query_with_config(
            "nonexistent.json",
            r#"ip="*******""#,
            "fofa",
            "quake"
        );

        assert!(result.is_err());
    }
}
