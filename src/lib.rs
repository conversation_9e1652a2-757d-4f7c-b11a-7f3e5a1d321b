//! # ConvertiX - 网络空间资产测绘查询语句转换工具
//! 
//! 一个用于在不同网络空间资产测绘平台间转换查询语句的Rust库。
//! 
//! ## 支持的平台
//! 
//! - FOFA
//! - Quake  
//! - Zoomeye
//! - Hunter
//! - Threatbook
//! 
//! ## 基本用法
//!
//! ```rust
//! use ConvertiX::QueryConverter;
//!
//! let converter = QueryConverter::new();
//! let result = converter.convert(
//!     r#"ip="*******" && port="80""#,
//!     "fofa",
//!     "quake"
//! ).unwrap();
//!
//! println!("转换结果: {}", result);
//! ```

pub mod converter;
pub mod config;

pub use converter::QueryConverter;
pub use config::{PlatformConfig, OperatorConfig, get_default_configs};

/// 便捷函数：快速转换查询语句
/// 
/// # 参数
/// 
/// * `query` - 要转换的查询语句
/// * `from` - 源平台名称
/// * `to` - 目标平台名称
/// 
/// # 返回值
/// 
/// 返回转换后的查询语句，如果转换失败则返回错误信息
/// 
/// # 示例
///
/// ```rust
/// use ConvertiX::convert_query;
///
/// let result = convert_query(
///     r#"ip="*******" && port="80""#,
///     "fofa",
///     "quake"
/// ).unwrap();
/// ```
pub fn convert_query(query: &str, from: &str, to: &str) -> Result<String, String> {
    let converter = QueryConverter::new();
    converter.convert(query, from, to)
}

/// 获取所有支持的平台列表
/// 
/// # 返回值
/// 
/// 返回包含所有支持平台名称的向量
/// 
/// # 示例
///
/// ```rust
/// use ConvertiX::get_supported_platforms;
///
/// let platforms = get_supported_platforms();
/// println!("支持的平台: {:?}", platforms);
/// ```
pub fn get_supported_platforms() -> Vec<String> {
    let converter = QueryConverter::new();
    converter.get_supported_platforms()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_fofa_to_quake_conversion() {
        let result = convert_query(
            r#"ip="*******" && port="80""#,
            "fofa",
            "quake"
        ).unwrap();
        
        assert!(result.contains("ip:"));
        assert!(result.contains("AND"));
    }

    #[test]
    fn test_quake_to_fofa_conversion() {
        let result = convert_query(
            r#"ip:"*******" AND port:"80""#,
            "quake",
            "fofa"
        ).unwrap();
        
        assert!(result.contains("ip="));
        assert!(result.contains("&&"));
    }

    #[test]
    fn test_same_platform_conversion() {
        let query = r#"ip="*******" && port="80""#;
        let result = convert_query(query, "fofa", "fofa").unwrap();
        
        assert_eq!(result, query);
    }

    #[test]
    fn test_unsupported_platform() {
        let result = convert_query(
            r#"ip="*******""#,
            "unsupported",
            "fofa"
        );
        
        assert!(result.is_err());
    }

    #[test]
    fn test_supported_platforms() {
        let platforms = get_supported_platforms();
        
        assert!(platforms.contains(&"fofa".to_string()));
        assert!(platforms.contains(&"quake".to_string()));
        assert!(platforms.contains(&"zoomeye".to_string()));
        assert!(platforms.contains(&"hunter".to_string()));
        assert!(platforms.contains(&"threatbook".to_string()));
    }
}
