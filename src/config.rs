use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 平台配置结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PlatformConfig {
    /// 字段映射 (如 "ip" -> "ip=")
    pub fields: HashMap<String, String>,
    /// 逻辑连接符映射
    pub operators: OperatorConfig,
}

/// 逻辑连接符配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperatorConfig {
    /// 等于操作符
    pub equal: String,
    /// 全等于操作符  
    pub strict_equal: String,
    /// 与操作符
    pub and: String,
    /// 或操作符
    pub or: String,
    /// 不等于操作符
    pub not_equal: String,
    /// 左括号
    pub left_paren: String,
    /// 右括号
    pub right_paren: String,
}

impl Default for PlatformConfig {
    fn default() -> Self {
        Self {
            fields: HashMap::new(),
            operators: OperatorConfig::default(),
        }
    }
}

impl Default for OperatorConfig {
    fn default() -> Self {
        Self {
            equal: "=".to_string(),
            strict_equal: "==".to_string(),
            and: "&&".to_string(),
            or: "||".to_string(),
            not_equal: "!=".to_string(),
            left_paren: "(".to_string(),
            right_paren: ")".to_string(),
        }
    }
}


