use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 平台配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformConfig {
    /// 字段映射 (如 "ip" -> "ip=")
    pub fields: HashMap<String, String>,
    /// 逻辑连接符映射
    pub operators: OperatorConfig,
}

/// 逻辑连接符配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperatorConfig {
    /// 等于操作符
    pub equal: String,
    /// 全等于操作符  
    pub strict_equal: String,
    /// 与操作符
    pub and: String,
    /// 或操作符
    pub or: String,
    /// 不等于操作符
    pub not_equal: String,
    /// 左括号
    pub left_paren: String,
    /// 右括号
    pub right_paren: String,
}

impl Default for PlatformConfig {
    fn default() -> Self {
        Self {
            fields: HashMap::new(),
            operators: OperatorConfig::default(),
        }
    }
}

impl Default for OperatorConfig {
    fn default() -> Self {
        Self {
            equal: "=".to_string(),
            strict_equal: "==".to_string(),
            and: "&&".to_string(),
            or: "||".to_string(),
            not_equal: "!=".to_string(),
            left_paren: "(".to_string(),
            right_paren: ")".to_string(),
        }
    }
}

/// 获取默认的平台配置
pub fn get_default_configs() -> HashMap<String, PlatformConfig> {
    let mut configs = HashMap::new();
    
    // FOFA配置
    let mut fofa_fields = HashMap::new();
    fofa_fields.insert("ip".to_string(), "ip=".to_string());
    fofa_fields.insert("port".to_string(), "port=".to_string());
    fofa_fields.insert("body".to_string(), "body=".to_string());
    
    configs.insert("fofa".to_string(), PlatformConfig {
        fields: fofa_fields,
        operators: OperatorConfig {
            equal: "=".to_string(),
            strict_equal: "==".to_string(),
            and: "&&".to_string(),
            or: "||".to_string(),
            not_equal: "!=".to_string(),
            left_paren: "(".to_string(),
            right_paren: ")".to_string(),
        },
    });
    
    // Quake配置
    let mut quake_fields = HashMap::new();
    quake_fields.insert("ip".to_string(), "ip:".to_string());
    quake_fields.insert("port".to_string(), "port:".to_string());
    quake_fields.insert("body".to_string(), "body:".to_string());
    
    configs.insert("quake".to_string(), PlatformConfig {
        fields: quake_fields,
        operators: OperatorConfig {
            equal: ":".to_string(),
            strict_equal: ":".to_string(),
            and: " AND ".to_string(),
            or: " OR ".to_string(),
            not_equal: " NOT ".to_string(),
            left_paren: "(".to_string(),
            right_paren: ")".to_string(),
        },
    });
    
    // Zoomeye配置 (与FOFA相同)
    let mut zoomeye_fields = HashMap::new();
    zoomeye_fields.insert("ip".to_string(), "ip=".to_string());
    zoomeye_fields.insert("port".to_string(), "port=".to_string());
    zoomeye_fields.insert("body".to_string(), "body=".to_string());
    
    configs.insert("zoomeye".to_string(), PlatformConfig {
        fields: zoomeye_fields,
        operators: OperatorConfig::default(),
    });
    
    // Hunter配置 (与FOFA相同)
    let mut hunter_fields = HashMap::new();
    hunter_fields.insert("ip".to_string(), "ip=".to_string());
    hunter_fields.insert("port".to_string(), "port=".to_string());
    hunter_fields.insert("body".to_string(), "body=".to_string());
    
    configs.insert("hunter".to_string(), PlatformConfig {
        fields: hunter_fields,
        operators: OperatorConfig::default(),
    });
    
    // Threatbook配置 (暂时与FOFA相同，可后续调整)
    let mut threatbook_fields = HashMap::new();
    threatbook_fields.insert("ip".to_string(), "ip=".to_string());
    threatbook_fields.insert("port".to_string(), "port=".to_string());
    threatbook_fields.insert("body".to_string(), "body=".to_string());
    
    configs.insert("threatbook".to_string(), PlatformConfig {
        fields: threatbook_fields,
        operators: OperatorConfig::default(),
    });
    
    configs
}
