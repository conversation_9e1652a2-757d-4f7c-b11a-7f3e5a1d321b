use crate::config::PlatformConfig;
use std::collections::HashMap;
use std::fs;
use std::path::Path;

/// 查询转换器
pub struct QueryConverter {
    configs: HashMap<String, PlatformConfig>,
}

impl QueryConverter {
    /// 从JSON文件创建转换器实例
    pub fn from_config_file<P: AsRef<Path>>(config_path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let config_content = fs::read_to_string(config_path)?;
        let configs: HashMap<String, PlatformConfig> = serde_json::from_str(&config_content)?;
        Ok(Self { configs })
    }
    

    
    /// 转换查询语句
    pub fn convert(&self, query: &str, from_platform: &str, to_platform: &str) -> Result<String, String> {
        let from_config = self.configs.get(from_platform)
            .ok_or_else(|| format!("不支持的源平台: {}", from_platform))?;
        let to_config = self.configs.get(to_platform)
            .ok_or_else(|| format!("不支持的目标平台: {}", to_platform))?;
        
        // 如果是同一个平台，直接返回
        if from_platform == to_platform {
            return Ok(query.to_string());
        }
        
        let mut result = query.to_string();
        
        // 转换逻辑操作符
        result = self.convert_operators(&result, from_config, to_config);
        
        // 转换字段前缀
        result = self.convert_fields(&result, from_config, to_config);
        
        Ok(result)
    }
    
    /// 转换逻辑操作符
    fn convert_operators(&self, query: &str, from_config: &PlatformConfig, to_config: &PlatformConfig) -> String {
        let mut result = query.to_string();
        
        // 处理不等于操作符 (需要特殊处理，因为可能包含字段名)
        result = self.convert_not_equal_operator(&result, from_config, to_config);
        
        // 处理其他操作符
        let operator_mappings = [
            (&from_config.operators.strict_equal, &to_config.operators.strict_equal),
            (&from_config.operators.equal, &to_config.operators.equal),
            (&from_config.operators.and, &to_config.operators.and),
            (&from_config.operators.or, &to_config.operators.or),
            (&from_config.operators.left_paren, &to_config.operators.left_paren),
            (&from_config.operators.right_paren, &to_config.operators.right_paren),
        ];
        
        for (from_op, to_op) in operator_mappings {
            if from_op != to_op {
                result = result.replace(from_op, to_op);
            }
        }
        
        result
    }
    
    fn convert_not_equal_operator(&self, query: &str, from_config: &PlatformConfig, to_config: &PlatformConfig) -> String {
        let mut result = query.to_string();
        
        if from_config.operators.not_equal == "!=" && to_config.operators.not_equal == "NOT" {
            let re = regex::Regex::new(r#"(\w+)!="([^"]*)"#).unwrap();
            result = re.replace_all(&result, |caps: &regex::Captures| {
                format!(r#"NOT {}:"{}""#, &caps[1], &caps[2])
            }).to_string();
        } else if from_config.operators.not_equal == "NOT" && to_config.operators.not_equal == "!=" {
            let re = regex::Regex::new(r#"NOT (\w+):"([^"]*)""#).unwrap();
            result = re.replace_all(&result, |caps: &regex::Captures| {
                format!(r#"{}!="{}""#, &caps[1], &caps[2])
            }).to_string();
        }
        
        result
    }
    
    fn convert_fields(&self, query: &str, from_config: &PlatformConfig, to_config: &PlatformConfig) -> String {
        let mut result = query.to_string();
        
        for (field_name, from_prefix) in &from_config.fields {
            if let Some(to_prefix) = to_config.fields.get(field_name) {
                if from_prefix != to_prefix {
                    result = result.replace(from_prefix, to_prefix);
                }
            }
        }
        
        result
    }
    
    pub fn get_supported_platforms(&self) -> Vec<String> {
        self.configs.keys().cloned().collect()
    }
}


