mod converter;
mod config;

use converter::QueryConverter;

fn main() {
    println!("=== 网络空间资产测绘查询语句转换工具 ===\n");

    let converter = QueryConverter::new();

    // 显示支持的平台
    println!("支持的平台: {:?}\n", converter.get_supported_platforms());

    // 示例1: FOFA -> 其他平台
    println!("【示例1】FOFA查询转换:");
    let fofa_query = r#"ip="*******" && (port="8800" && body!="test")"#;
    println!("原始FOFA查询: {}", fofa_query);

    for platform in &["quake", "zoomeye", "hunter", "threatbook"] {
        if let Ok(converted) = converter.convert(fofa_query, "fofa", platform) {
            println!("转换为{}查询: {}", platform.to_uppercase(), converted);
        }
    }

    // 示例2: Quake -> 其他平台
    println!("\n【示例2】Quake查询转换:");
    let quake_query = r#"ip:"***********" AND (port:"80" OR port:"443") AND NOT body:"404""#;
    println!("原始Quake查询: {}", quake_query);

    for platform in &["fofa", "zoomeye", "hunter"] {
        if let Ok(converted) = converter.convert(quake_query, "quake", platform) {
            println!("转换为{}查询: {}", platform.to_uppercase(), converted);
        }
    }

    // 示例3: 导出配置
    println!("\n【示例3】导出当前配置到JSON:");
    if let Ok(json_config) = converter.export_config_json() {
        println!("配置已导出 (部分内容):");
        // 只显示前几行，避免输出过长
        let lines: Vec<&str> = json_config.lines().take(10).collect();
        for line in lines {
            println!("{}", line);
        }
        println!("... (更多内容请查看完整JSON文件)");
    }

    println!("\n=== 转换完成 ===");
}