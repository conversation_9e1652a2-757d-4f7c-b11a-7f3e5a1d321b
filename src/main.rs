use clap::{Parser, ValueEnum};
use serde_json::json;
use std::fs;
use std::path::PathBuf;
use std::process;

mod converter;
mod config;

use converter::QueryConverter;

/// Cyberspace asset mapping query statement conversion tool
#[derive(Parser)]
#[command(name = "ConvertiX")]
#[command(about = "Cyberspace Asset Mapping Platform Query Statement Conversion Tool")]
#[command(version = "1.0")]
struct Args {
    /// Configuration file path
    #[arg(short = 'c', long = "config", default_value = "config.json")]
    config: PathBuf,

    /// Query statement
    #[arg(short = 'q', long = "query")]
    query: String,

    /// Source platform of the query statement
    #[arg(short = 'p', long = "platform")]
    platform: String,

    /// Output format
    #[arg(short = 'f', long = "format", default_value = "raw")]
    format: OutputFormat,

    /// Output file path (optional, defaults to stdout)
    #[arg(short = 'o', long = "output")]
    output: Option<PathBuf>,
}

#[derive(Clone, ValueEnum)]
enum OutputFormat {
    /// Raw text output
    Raw,
    /// JSON format output
    Json,
}

fn main() {
    let args = Args::parse();

    // Load configuration file
    let converter = match QueryConverter::from_config_file(&args.config) {
        Ok(converter) => converter,
        Err(e) => {
            eprintln!("❌ Failed to load configuration file: {}", e);
            eprintln!("Please ensure configuration file '{}' exists and is properly formatted", args.config.display());
            process::exit(1);
        }
    };

    // Get supported platforms list
    let supported_platforms = converter.get_supported_platforms();

    // Validate if the input platform is supported
    if !supported_platforms.contains(&args.platform) {
        eprintln!("❌ Unsupported platform: {}", args.platform);
        eprintln!("Supported platforms: {}", supported_platforms.join(", "));
        process::exit(1);
    }

    // Convert to all other platforms
    let mut conversions = Vec::new();
    let mut converted_count = 0;

    for target_platform in &supported_platforms {
        if target_platform != &args.platform {
            match converter.convert(&args.query, &args.platform, target_platform) {
                Ok(converted_query) => {
                    conversions.push((target_platform.clone(), converted_query.clone()));
                    converted_count += 1;
                }
                Err(e) => {
                    eprintln!("❌ Failed to convert to {}: {}", target_platform, e);
                }
            }
        }
    }

    // Generate output based on format
    let output_content = match args.format {
        OutputFormat::Raw => generate_raw_output(&args, &conversions, converted_count),
        OutputFormat::Json => generate_json_output(&args, &conversions, converted_count),
    };

    // Write output to file or stdout
    match args.output {
        Some(output_path) => {
            if let Err(e) = fs::write(&output_path, output_content) {
                eprintln!("❌ Failed to write to output file '{}': {}", output_path.display(), e);
                process::exit(1);
            }
            println!("✅ Output written to: {}", output_path.display());
        }
        None => {
            print!("{}", output_content);
        }
    }
}

fn generate_raw_output(args: &Args, conversions: &[(String, String)], converted_count: usize) -> String {
    let mut output = String::new();

    output.push_str("🔄 ConvertiX - Query Statement Conversion Tool\n");
    output.push_str(&format!("📁 Configuration file: {}\n", args.config.display()));
    output.push_str(&format!("🎯 Source platform: {}\n", args.platform));
    output.push_str(&format!("📝 Original query: {}\n", args.query));
    output.push_str("\n");

    for (platform, query) in conversions {
        output.push_str(&format!("✅ {} query: {}\n", platform.to_uppercase(), query));
    }

    if converted_count > 0 {
        output.push_str(&format!("\n🎉 Successfully converted to {} platforms\n", converted_count));
    } else {
        output.push_str("\n⚠️  No target platforms available for conversion\n");
    }

    output
}

fn generate_json_output(args: &Args, conversions: &[(String, String)], converted_count: usize) -> String {
    let mut converted_queries = serde_json::Map::new();

    for (platform, query) in conversions {
        converted_queries.insert(platform.clone(), json!(query));
    }

    let result = json!({
        "source_platform": args.platform,
        "original_query": args.query,
        "converted_queries": converted_queries,
    });

    serde_json::to_string_pretty(&result).unwrap_or_else(|e| {
        eprintln!("❌ Failed to serialize JSON output: {}", e);
        process::exit(1);
    })
}