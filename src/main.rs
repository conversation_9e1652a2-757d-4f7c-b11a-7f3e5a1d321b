use clap::Parser;
use std::path::PathBuf;
use std::process;

mod converter;
mod config;

use converter::QueryConverter;

#[derive(Parser)]
#[command(name = "ConvertiX")]
#[command(about = "Cyberspace Asset Mapping Platform Query Statement Conversion Tool")]
#[command(version = "0.1.0")]
struct Args {
    /// 配置文件路径
    #[arg(short = 'c', long = "config", default_value = "config.json")]
    config: PathBuf,

    /// 查询语句
    #[arg(short = 'q', long = "query")]
    query: String,

    /// 查询语句所属平台
    #[arg(short = 'p', long = "platform")]
    platform: String,
}

fn main() {
    let args = Args::parse();

    // 加载配置文件
    let converter = match QueryConverter::from_config_file(&args.config) {
        Ok(converter) => converter,
        Err(e) => {
            eprintln!("❌ 加载配置文件失败: {}", e);
            eprintln!("请确保配置文件 '{}' 存在且格式正确", args.config.display());
            process::exit(1);
        }
    };

    // 获取支持的平台列表
    let supported_platforms = converter.get_supported_platforms();

    // 验证输入的平台是否支持
    if !supported_platforms.contains(&args.platform) {
        eprintln!("❌ 不支持的平台: {}", args.platform);
        eprintln!("支持的平台: {}", supported_platforms.join(", "));
        process::exit(1);
    }

    println!("🔄 ConvertiX - 查询语句转换工具");
    println!("📁 配置文件: {}", args.config.display());
    println!("🎯 源平台: {}", args.platform);
    println!("📝 原始查询: {}", args.query);
    println!();

    // 转换到所有其他平台
    let mut converted_count = 0;
    for target_platform in &supported_platforms {
        if target_platform != &args.platform {
            match converter.convert(&args.query, &args.platform, target_platform) {
                Ok(converted_query) => {
                    println!("✅ {} 查询: {}", target_platform.to_uppercase(), converted_query);
                    converted_count += 1;
                }
                Err(e) => {
                    eprintln!("❌ 转换到 {} 失败: {}", target_platform, e);
                }
            }
        }
    }

    if converted_count > 0 {
        println!("\n🎉 成功转换到 {} 个平台", converted_count);
    } else {
        println!("\n⚠️  没有可转换的目标平台");
    }
}