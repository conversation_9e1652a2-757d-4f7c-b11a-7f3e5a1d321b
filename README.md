# ConvertiX - 网络空间资产测绘查询语句转换工具

一个用Rust编写的网络空间资产测绘平台查询语句转换工具，支持在不同平台间转换查询语法。

## 支持的平台

- **FOFA** - 白帽汇网络空间资产搜索引擎
- **Quake** - 360网络空间测绘系统  
- **Zoomeye** - 知道创宇网络空间搜索引擎
- **Hunter** - 鹰图平台
- **Threatbook** - 微步在线威胁情报平台

## 功能特性

- 🔄 **多平台转换**: 支持5大主流网络空间测绘平台间的语句转换
- ⚙️ **配置化设计**: 通过JSON配置文件轻松扩展和修改平台语法规则
- 🎯 **精确转换**: 支持字段映射和逻辑操作符的准确转换
- 🧩 **模块化架构**: 代码结构清晰，易于维护和扩展
- 📝 **简单易用**: 提供简洁的API接口

## 快速开始

### 编译运行

```bash
# 克隆项目
git clone <your-repo-url>
cd ConvertiX

# 编译项目
cargo build --release

# 运行示例
cargo run
```

### 基本用法

```rust
use converter::QueryConverter;

fn main() {
    let converter = QueryConverter::new();
    
    // FOFA查询转换为Quake查询
    let fofa_query = r#"ip="*******" && port="80""#;
    let quake_query = converter.convert(fofa_query, "fofa", "quake").unwrap();
    
    println!("FOFA: {}", fofa_query);
    println!("Quake: {}", quake_query);
}
```

## 语法对照表

### 逻辑操作符

| 操作 | FOFA | Quake | Zoomeye | Hunter | Threatbook |
|------|------|-------|---------|--------|------------|
| 等于 | `=` | `:` | `:` | `=` | `:` |
| 全等于 | `==` | `:` | `:` | `==` | `:` |
| 与 | `&&` | `AND` | `+` | `&&` | `AND` |
| 或 | `\|\|` | `OR` | `\|` | `\|\|` | `OR` |
| 非 | `!=` | `NOT` | `-` | `!=` | `NOT` |

### 字段映射示例

| 字段 | FOFA | Quake | Zoomeye | Hunter |
|------|------|-------|---------|--------|
| IP地址 | `ip=` | `ip:` | `ip:` | `ip=` |
| 端口 | `port=` | `port:` | `port:` | `port=` |
| 响应体 | `body=` | `response.body:` | `body:` | `web.body=` |
| 标题 | `title=` | `response.title:` | `title:` | `web.title=` |

## 配置文件

项目包含一个标准的JSON配置文件 `config.json`，你可以根据需要修改或扩展：

```json
{
  "fofa": {
    "fields": {
      "ip": "ip=",
      "port": "port=",
      "body": "body="
    },
    "operators": {
      "equal": "=",
      "strict_equal": "==",
      "and": " && ",
      "or": " || ",
      "not_equal": "!=",
      "left_paren": "(",
      "right_paren": ")"
    }
  }
}
```

## API 文档

### QueryConverter

主要的转换器类，提供以下方法：

#### `new() -> Self`
创建一个使用默认配置的转换器实例。

#### `from_json(json_str: &str) -> Result<Self, Box<dyn std::error::Error>>`
从JSON字符串加载配置创建转换器实例。

#### `convert(query: &str, from_platform: &str, to_platform: &str) -> Result<String, String>`
转换查询语句从一个平台到另一个平台。

#### `add_platform_config(platform: &str, config: PlatformConfig)`
添加或更新平台配置。

#### `get_supported_platforms() -> Vec<String>`
获取支持的平台列表。

#### `export_config_json() -> Result<String, serde_json::Error>`
导出当前配置为JSON字符串。

## 转换示例

### FOFA → Quake
```
输入: ip="*******" && (port="8800" && body!="test")
输出: ip:"*******" AND (port:"8800" AND NOT body:"test")
```

### Quake → FOFA  
```
输入: ip:"***********" AND port:"80" OR port:"443"
输出: ip="***********" && port="80" || port="443"
```

## 项目结构

```
ConvertiX/
├── src/
│   ├── main.rs          # 主程序入口和示例
│   ├── converter.rs     # 核心转换逻辑
│   └── config.rs        # 配置结构定义
├── config.json          # 标准配置文件
├── Cargo.toml          # 项目配置
└── README.md           # 项目说明
```

## 扩展指南

### 添加新平台

1. 在 `config.json` 中添加新平台的配置
2. 或者使用 `add_platform_config()` 方法动态添加
3. 新平台将自动支持与其他平台的互转

### 添加新字段

在对应平台的 `fields` 配置中添加新的字段映射即可。

## 依赖项

- `serde` - JSON序列化/反序列化
- `serde_json` - JSON处理
- `regex` - 正则表达式支持

## 许可证

[MIT License](LICENSE)

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
